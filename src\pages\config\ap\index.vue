<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { computed, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { isEmptyArray, isNullOrUndefined } from '@/@core/utils/helpers'
import { requiredValidator } from '@/@core/utils/validators'
import { router } from '@/plugins/1.router'
import {
  BAND_WIDTH_2G,
  CHANNEL_ARR_2G,
  CHANNEL_ARR_5G,
  COUNTRY_OPTIONS,
  ENCRYPTION_TYPE,
  NET_TYPE,
  PROTOCOL_2G,
  PROTOCOL_5G,
  SPEED_LIMIT_TYPE,
  TEMPLATE_DISTRIBUTION_METHOD,
  TX_POWER_2G,
  TX_POWER_5G,
} from '@/utils/constants'
import { getRandomUUID, isByteLengthInRange } from '@layouts/utils'

const { t } = useI18n()
const deviceName = ref('')
const loading = ref(false)

// 定时器相关变量
const refreshTimer = ref<NodeJS.Timeout | null>(null)
const autoRefresh = ref(true) // 默认开启自动刷新
const refreshInterval = ref(30000) // 30秒刷新间隔

// 对constants中的国际化常量进行本地化处理
const COUNTRY_OPTIONS_LOCALIZED = computed(() => {
  return COUNTRY_OPTIONS.map(item => ({
    ...item,
    label: item.label, // 国家代码不需要翻译
  }))
})

// 对NET_TYPE进行本地化处理
const NET_TYPE_LOCALIZED = computed(() => {
  return NET_TYPE.map(item => {
    if (item.label === 'NetworkConfig.Modes.RouterMode')
      return { ...item, label: t('NetworkConfig.Modes.RouterMode') }
    else if (item.label === 'NetworkConfig.Modes.APMode')
      return { ...item, label: t('NetworkConfig.Modes.APMode') }

    return item
  })
})

// 对TEMPLATE_DISTRIBUTION_METHOD进行本地化处理
const TEMPLATE_DISTRIBUTION_METHOD_LOCALIZED = computed(() => {
  return TEMPLATE_DISTRIBUTION_METHOD.map(item => {
    if (item.label === 'template.immediate')
      return { ...item, label: t('template.immediate') }

    return item
  })
})

// 对SPEED_LIMIT_TYPE进行本地化处理
const SPEED_LIMIT_TYPE_LIST = computed(() => {
  return SPEED_LIMIT_TYPE.map(item => ({
    ...item,
    label: t(item.label), // 国家代码不需要翻译
  }))
})

// 对TX_POWER_2G进行本地化处理
const TX_POWER_2G_LOCALIZED = computed(() => {
  return TX_POWER_2G.map(item => {
    if (item.label === 'txPower.penetration')
      return { ...item, label: t('txPower.penetration') }
    else if (item.label === 'txPower.normal')
      return { ...item, label: t('txPower.normal') }
    else if (item.label === 'txPower.saving')
      return { ...item, label: t('txPower.saving') }

    return item
  })
})

// 对TX_POWER_5G进行本地化处理
const TX_POWER_5G_LOCALIZED = computed(() => {
  return TX_POWER_5G.map(item => {
    if (item.label === 'txPower.penetration')
      return { ...item, label: t('txPower.penetration') }
    else if (item.label === 'txPower.normal')
      return { ...item, label: t('txPower.normal') }
    else if (item.label === 'txPower.saving')
      return { ...item, label: t('txPower.saving') }

    return item
  })
})

const selectedModel = ref()
const route = useRoute()
const selectedRows = ref([])

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)

// 表头
const headers = [
  { title: t('Config.AP.DeviceName'), key: 'user_name', sortable: false },
  { title: t('Config.AP.Model'), key: 'model', sortable: false },
  { title: t('Config.AP.SerialNumber'), key: 'sn', sortable: false },
  { title: t('Config.AP.IPAddress'), key: 'ip', sortable: false },
  { title: t('Config.AP.MACAddress'), key: 'mac', sortable: false },
  { title: t('Config.AP.Status'), key: 'onoff', sortable: false },
]

const templateList = ref([])

// 数据
const apList = ref({
  total: 0,
  list: [],
})

// 动态计算表格数据
const list = computed((): any[] => {
  let arr = apList.value.list

  // 型号筛选
  if (selectedModel.value)
    arr = arr.filter((item: any) => item.model === selectedModel.value)

  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return arr.slice(start, end)
})

//  监听list  变化设置 selectedRows.value = []
watch(list, () => {
  selectedRows.value = []
})

const totalProduct = computed(() => {
  let arr = apList.value.list

  // 型号筛选
  if (selectedModel.value)
    arr = arr.filter((item: any) => item.model === selectedModel.value)

  return arr.length
})

const distributeTemplateDialog = ref(false)

const openDistributeTemplateDialog = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.error(t('Config.AP.SelectDevices'))

    return
  }
  resetDistributeForm()
  $api('', { requestType: 516 }).then(res => {
    if (res.err_code === 0)
      templateList.value = res.info.templates
  })
  step.value = 0
  distributeProgress.value = 0
  successNum.value = 0
  failNum.value = 0
  distributeTemplateDialog.value = true
}

const closeDistributeTemplateDialog = () => {
  distributeTemplateDialog.value = false
  step.value = 0
}

const step = ref(0)
const formRef1 = useTemplateRef('formRef1')
const formRef2 = useTemplateRef('formRef2')

const formRef3 = ref()
const formRef4 = ref()
const formRef5 = ref()
const formRef6 = ref()
const formRef = [formRef3, formRef4, formRef5, formRef6]

const nextStep = () => {
  if (step.value < 2) {
    if (step.value === 0) {
      formRef1.value?.validate().then(result => {
        if (!result.valid)
          return

        // selectedRows.value 是选中的sn，遍历selectedRows.value 获取aplist.value.list 中sn存在的对象model
        const models = selectedRows.value.map((item: any) => {
          const obj = apList.value.list.find((ap: any) => ap.sn === item)

          return obj.model
        })

        // 去重model
        const uniqueModels = [...new Set(models)]

        // 如果去重后的长度不等于1，说明选择了不同型号的设备
        if (uniqueModels.length !== 1) {
          ElMessage.error(t('Config.AP.SelectSameModel'))

          return
        }

        // 获取templateList 中mdistributeForm.templateId 的model  并打印
        const template = templateList.value.find((item: any) => item.id === distributeForm.templateId)

        console.log('template', template.model)
        console.log('model', uniqueModels[0])
        if (template.model === uniqueModels[0])
          step.value += 1
        else
          ElMessage.error(`${t('PleaseSelect')} ${template.model} ${t('Config.AP.ModelType')}`)
      })
    }
    else if (step.value === 1) {
      formRef2.value?.validate().then(result => {
        if (!result.valid)
          return

        step.value += 1
      })
    }
  }
}

const distributeDisabled = computed(() => {
  if (step.value === 1) {
    if (distributeForm.distributeType === null)
      return true

    if (distributeForm.distributeType === 1)
      return !distributeForm.distributeTime
    else
      return false
  }

  return true
})

const distributeForm = reactive({
  templateId: null,
  distributeType: null,
  distributeTime: '',
  distributeTimeType: '0',
})

const resetDistributeForm = () => {
  distributeForm.templateId = null
  distributeForm.distributeType = null
  distributeForm.distributeTime = ''
  distributeForm.distributeTimeType = '0'
}

const goToCreate = () => {
  router.push({ name: 'config-mode' })
}

// 0-未开始 1-进行中 2-完成
const distributeState = ref(0)
const distributeProgress = ref(0)
const totalNum = ref(0)
const successNum = ref(0)
const failNum = ref(0)
const exportData = ref([])

// 添加计时器变量
const startTime = ref(0)

// 下发配置
const distributeHandle = () => {
  if (step.value === 1 && !distributeDisabled.value) {
    $api('', {
      requestType: 519,
      data: {
        id: distributeForm.templateId,
        ap_sn_list: `${selectedRows.value.join(' ')} `,
      },
    }).then(res => {
      totalNum.value = selectedRows.value.length
      console.log('distributeHandle', res)
      if (res.err_code === 0)
        distributeState.value = 3

      setTimeout(() => {
        dealRequestData()
      }, 3000)
    })
  }
}

const dealRequestData = () => {
  // 首次调用时记录开始时间
  if (!startTime.value)
    startTime.value = Date.now()

  // 计算超时时间（AP数量*5秒）
  const timeout = totalNum.value * 5000
  const currentTime = Date.now()
  const elapsedTime = currentTime - startTime.value

  // 检查是否超时
  if (elapsedTime >= timeout) {
    distributeState.value = 2

    // 更新未完成的设备为失败状态
    const unfinishedNum = totalNum.value - successNum.value - failNum.value

    failNum.value = Math.floor(unfinishedNum)
    ElMessage.error(t('Config.AP.ConfigTimeout'))
    startTime.value = 0

    return
  }

  $api('', {
    requestType: 507,
    data: {},
  }).then(res => {
    if (res.err_code === 0) {
      // 筛选数组res.info.apstatus 中status不为0 的所有对象组成新数组
      const statusList = res.info.apstatus.filter((item: any) => item.status && item.status !== '0')

      exportData.value = res.info.apstatus
      successNum.value = statusList.filter((item: any) => item.status === '2' || item.status === '4').length
      failNum.value = statusList.filter((item: any) => item.status === '99' || item.status === '999').length
      distributeProgress.value = Math.floor((successNum.value / totalNum.value) * 100)

      if (successNum.value === totalNum.value) {
        distributeState.value = 2
        ElMessage.success(t('Config.AP.ConfigAllSuccess'))
        startTime.value = 0
      }
      else if (successNum.value + failNum.value === totalNum.value) {
        distributeState.value = 2
        ElMessage.success(t('Config.AP.ConfigPartialSuccess'))
        startTime.value = 0
      }
      else {
        distributeState.value = 3

        // 检查剩余时间是否足够下一次轮询
        const remainingTime = timeout - elapsedTime
        if (remainingTime >= 1000) {
          setTimeout(() => {
            dealRequestData()
          }, 1000)
        }
        else {
          // 剩余时间不足，判定为超时
          distributeState.value = 2

          // 更新未完成的设备为失败状态
          failNum.value = Math.floor(totalNum.value - successNum.value)
          ElMessage.error(t('Config.AP.ConfigTimeout'))
          startTime.value = 0
        }
      }
    }
  }).catch(() => {
    distributeState.value = 2

    // 更新未完成的设备为失败状态
    failNum.value = Math.floor(totalNum.value - successNum.value)

    ElMessage.error(t('Config.AP.RequestFailed'))
    startTime.value = 0
  })
}

const distributeSuccess = () => {
  closeDistributeTemplateDialog()
  step.value = 0
  distributeState.value = 0
  formRef1.value?.reset()
  formRef2.value?.reset()
}

const exportLog = () => {
  // 准备CSV数据
  const csvHeaders = [t('Config.AP.SerialNumber'), t('Config.AP.ConfigStatus'), t('Config.AP.ConfigTime')]
  const arr = exportData.value.filter((item: any) => selectedRows.value.includes(item.sn))

  // 过滤有效的状态数据（排除end和无效数据）
  const validStatusList = arr.filter((item: any) => item.sn && item.status !== undefined && item.sn.length === 20)
    .map((item: any) => ({
      sn: item.sn,
      statusText: item.status === '2' || item.status === '4'
        ? t('Config.AP.Success')
        : item.status === '0' ? t('Config.AP.NotStarted') : t('Config.AP.Failed'),
      time: new Date().toLocaleString(),
    }))

  // 生成CSV内容，为序列号添加引号以防止Excel将其转换为科学计数法
  const csvContent = [
    csvHeaders.join(','),
    ...validStatusList.map(item => [
      `="${item.sn}"`, // 添加引号和等号，强制Excel将其作为文本处理
      item.statusText,
      item.time,
    ].join(',')),
  ].join('\n')

  // 创建Blob对象
  const blob = new Blob([`\uFEFF${csvContent}`], {
    type: 'text/csv;charset=utf-8;',
  })

  // 创建下载链接
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  // 设置文件名
  const fileName = `${t('Config.AP.ConfigDistributionRecord')}_${new Date().toLocaleDateString().replace(/\//g, '')}.csv`

  link.setAttribute('href', url)
  link.setAttribute('download', fileName)
  link.style.visibility = 'hidden'

  // 添加到页面并触发下载
  document.body.appendChild(link)
  link.click()

  // 清理
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const modelList = ref([] as { label: string; value?: string }[])

// 启动定时器
const startRefreshTimer = () => {
  if (refreshTimer.value)
    clearInterval(refreshTimer.value)

  if (autoRefresh.value) {
    refreshTimer.value = setInterval(() => {
      // 只有在没有加载状态时才刷新，避免重复请求
      if (!loading.value)
        getDeviceList(false) // 传入false表示定时器刷新，不显示loading
    }, refreshInterval.value)
  }
}

// 停止定时器
const stopRefreshTimer = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 切换自动刷新状态
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value)
    startRefreshTimer()
  else
    stopRefreshTimer()
}

// watch 监听deviceName
watch(deviceName, () => {
  getDeviceList()

  // 重启定时器以确保使用新的筛选条件
  if (autoRefresh.value) {
    stopRefreshTimer()
    startRefreshTimer()
  }
})

// watch 监听selectedModel，切换型号时重置页码
watch(selectedModel, () => {
  page.value = 1

  // 重启定时器以确保使用新的筛选条件
  if (autoRefresh.value) {
    stopRefreshTimer()
    startRefreshTimer()
  }
})

// 获取设备列表
const getDeviceList = (showLoading = true) => {
  if (showLoading) {
    loading.value = true
    page.value = 1
  }
  $api('', { requestType: 500 }).then(res => {
    if (res.err_code === 0) {
      let arr = res.info.ap.filter((item: any) => item.sn)

      // 设备名筛选
      if (deviceName.value) {
        arr = arr.filter((item: any) =>
          item.user_name && item.user_name.toLowerCase().includes(deviceName.value.toLowerCase()),
        )
      }

      apList.value.total = arr.length || 0
      apList.value.list = arr.map(item => {
        return {
          ...item,
          type: 'AP',
        }
      })

      const mainList = res.info.ap.filter((item: any) => item.sn) || []
      const models = new Set([]) as Set<string>

      mainList.forEach((item: any) => {
        if (item.model)
          models.add(item.model)
      })
      modelList.value = Array.from(models).map(item => {
        return {
          label: item,
          value: item,
        }
      })
    }
  }).catch(() => {
  }).finally(() => {
    if (showLoading)
      loading.value = false
  })
}

const currentTab = ref(0)

const nextStepCreate = () => {
  formRef[currentTab.value].value?.validate().then(res => {
    if (res.valid)
      currentTab.value += 1
  })
}

const tabList = ref([
  { label: t('Config.Mode.BasicSettings'), value: 0 },
  { label: t('Config.AP.SSIDConfig'), value: 1 },
  { label: t('Config.AP.AdvancedConfig'), value: 2 },
  { label: t('Config.AP.RoamingConfig'), value: 3 },
])

let templateForm = reactive({
  id: '', // （随机生成32位以内可含英文或者数字或下划线的字符串，传值），模板的唯一识别码,id不可变，通过id绑定模板
  tpl_name: '', // 模板名称，支持中文（UTF-8）一个中文占三字节
  model: '', // AP型号
  tpl_bands: '', // 适用频段
  ssid_count: '', // SSID数量
  modified_at: '', //	最后修改时间
  description: '', //	备注，支持中文（UTF-8）一个中文占三字节
  ssid_2g: '', //	2G SSID
  ssid_5g: '', //	5G SSID
  key_2g: '', //	2G密码
  key_5g: '', //	5G密码
  ssid_type: '1', //	1：SSID双频合一 0：SSID分开
  net_type: undefined, //	0：路由模式 1：AP模式
  ap_lan_ip: '', //	LAN IP
  ap_lan_mask: '', //	LAN子网掩码
  wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
  wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
  wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
  wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
  wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
  wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
  wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
  wifiCountry_5G: 'CN', //	5.8G 国家代码
  wifiChannel_2G: 'auto', //	2.4G 信道
  wifiChannel_5G: 'auto', //	5.8G 信道
  wifiHwMode_2G: undefined, //	2.4G协议
  wifiHwMode_5G: undefined, //	5.8G协议
  wifiHtMode_2G: 'HT20', //	2.4G带宽
  wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
  wifiHtMode_5G: 'HT160', //	5.8G带宽
  wifiTxpower_2G: '', // 2.4G信号调节
  wifiTxpower_5G: '', // 5.8G信号调节
  wifi80211r_2G: '', //	2.4G快速漫游r协议
  wifi80211r_5G: '', //	5.8G快速漫游r协议
  wifiAuthmode_2G: undefined, //	2.4G加密方式选项
  wifiAuthmode_5G: undefined, //	5.8G加密方式选项
  wifiWpa3_2G: '', //	2.4G wpa3 开启标志
  wifiWpa3_5G: '', //	5.8G wpa3 开启标志
  // 双频合一的参数
  ssid: '', // SSID名称
  encryptionType: undefined, // 加密类型
  password: '', // 密码
  isolate: false, // 隔离
  // 漫游配置
  quickRoaming: false, // 快速漫游
  roamingProtocol: undefined, // 漫游协议
  // 以下是暂不使用的变量
  networkLimit: undefined, // 限速
  upstreamLimit: '', // 上行限制
  downstreamLimit: '', // 下行限制
  wifiMaxsta_2G: '128',
  wifiMaxsta_5G: '128',
})

const handleCountryChange = (type: '2G' | '5G', value: string) => {
  if (type === '2G')
    templateForm.wifiChannel_2G = 'auto'

  else
    templateForm.wifiChannel_5G = 'auto'
}

const validAllForm = async () => {
  for (let i = 0; i < formRef.length; i++) {
    const validator = formRef[i]
    const valid = await validator.value?.validate()
    if (!valid?.valid) {
      currentTab.value = i

      return false
    }
  }

  return true
}

const show2GPassword = ref(false)
const show5GPassword = ref(false)
const showMergePassword = ref(false)

// 模板ID变量
let templateId = ''

// SSID类型变化处理函数
const ssidTypeChange = () => {
  // 重置表单验证状态
  if (formRef4.value)
    formRef4.value.resetValidation()
}

const save = async () => {
  if (!(await validAllForm()))
    return

  let {
    id,
    tpl_name,
    tpl_bands,
    model,
    ssid_count,
    description,
    ssid_2g,
    ssid_5g,
    key_2g,
    key_5g,
    ssid_type,
    net_type,
    ap_lan_ip,
    ap_lan_mask,
    wifiOnOff_2G,
    wifiOnOff_5G,
    wifiApIsolate_2G,
    wifiApIsolate_5G,
    wifiEnable_2G,
    wifiEnable_5G,
    wifiCountry_2G,
    wifiCountry_5G,
    wifiChannel_2G,
    wifiChannel_5G,
    wifiHwMode_2G,
    wifiHwMode_5G,
    wifiHtMode_2G,
    wifiForce40MHzMode_2G,
    wifiHtMode_5G,
    wifiTxpower_2G,
    wifiTxpower_5G,
    wifi80211r_2G,
    wifi80211r_5G,
    wifiAuthmode_2G,
    wifiAuthmode_5G,
    wifiWpa3_2G,
    wifiWpa3_5G,
    quickRoaming,
    roamingProtocol,
    wifiMaxsta_2G,
    wifiMaxsta_5G,
    ssid,
    password,
    isolate,
    encryptionType,
  } = templateForm

  if (ssid_type == '1') {
    ssid_2g = ssid
    ssid_5g = ssid
    key_2g = password
    key_5g = password
    wifiApIsolate_2G = isolate ? '1' : '0'
    wifiApIsolate_5G = isolate ? '1' : '0'
    wifiAuthmode_2G = encryptionType
    wifiAuthmode_5G = encryptionType
    tpl_bands = '2.4G/5G'
  }
  else {
    if (wifiOnOff_5G == '0')
      tpl_bands = '5G'
    else if (wifiOnOff_2G == '0')
      tpl_bands = '2.4G'
    else
      tpl_bands = '-'
  }

  if (quickRoaming && roamingProtocol === 0) {
    wifi80211r_2G = '1'
    wifi80211r_5G = '1'
  }
  else {
    wifi80211r_2G = '0'
    wifi80211r_5G = '0'
  }

  if (wifiAuthmode_2G === 'psk2')
    wifiWpa3_2G = '1'
  else
    wifiWpa3_2G = '0'

  if (wifiAuthmode_5G === 'psk2')
    wifiWpa3_5G = '1'
  else
    wifiWpa3_5G = '0'

  if (wifiAuthmode_2G == 'none')
    key_2g = ''

  if (wifiAuthmode_5G == 'none')
    key_5g = ''

  $api('', {
    requestType: dialogStatus.value === 1 ? 518 : 515,
    data: {
      id: dialogStatus.value === 1 ? id : getRandomUUID(),
      tpl_name,
      tpl_bands,
      ssid_count,
      model,
      modified_at: new Date().toISOString(),
      description,
      ssid_2g,
      ssid_5g,
      key_2g,
      key_5g,
      ssid_type,
      net_type,
      ap_lan_ip,
      ap_lan_mask,
      wifiOnOff_2G,
      wifiOnOff_5G,
      wifiApIsolate_2G,
      wifiApIsolate_5G,
      wifiEnable_2G,
      wifiEnable_5G,
      wifiCountry_2G,
      wifiCountry_5G,
      wifiChannel_2G,
      wifiChannel_5G,
      wifiHwMode_2G,
      wifiHwMode_5G,
      wifiHtMode_2G,
      wifiForce40MHzMode_2G,
      wifiHtMode_5G,
      wifiTxpower_2G,
      wifiTxpower_5G,
      wifi80211r_2G,
      wifi80211r_5G,
      wifiAuthmode_2G,
      wifiAuthmode_5G,
      wifiWpa3_2G,
      wifiWpa3_5G,
      wifiMaxsta_2G,
      wifiMaxsta_5G,
    },
  }).then(res => {
    if (res.err_code === 0) {
      ElMessage.success(t('Config.AP.SaveSuccess'))
      createTemplateDialog.value = false
      resetForm()
      resetList()
    }
    else {
      ElMessage.error(t('Config.AP.SaveFailed'))
    }
  })
}

const resetList = () => {
  page.value = 1
  getDeviceList()
}

// 新建模版
const createTemplateDialog = ref(false)

// 0-添加 1-编辑
const dialogStatus = ref(0)

const createNewTemplate = () => {
  dialogStatus.value = 0 // 新建状态
  openDialog()
}

const createNewTemplateA = () => {
  distributeTemplateDialog.value = false
  dialogStatus.value = 0 // 新建状态
  openDialog()
}

const openDialog = () => {
  resetForm()
  createTemplateDialog.value = true
  currentTab.value = 0
}

const resetForm = () => {
  templateForm = reactive({
    id: '', // （随机生成32位以内可含英文或者数字或下划线的字符串，传值），模板的唯一识别码,id不可变，通过id绑定模板
    tpl_name: '', // 模板名称，支持中文（UTF-8）一个中文占三字节
    model: modelList.value.length > 0 ? modelList.value[0].value : '', // AP型号，默认选中第一个
    tpl_bands: '', // 适用频段
    ssid_count: '', // SSID数量
    modified_at: '', //	最后修改时间
    description: '', //	备注，支持中文（UTF-8）一个中文占三字节
    ssid_2g: '', //	2G SSID
    ssid_5g: '', //	5G SSID
    key_2g: '', //	2G密码
    key_5g: '', //	5G密码
    ssid_type: '1', //	1：SSID双频合一 0：SSID分开
    net_type: '1', //	0：路由模式 1：AP模式，默认AP模式
    ap_lan_ip: '', //	LAN IP
    ap_lan_mask: '', //	LAN子网掩码
    wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
    wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
    wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
    wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
    wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
    wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
    wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
    wifiCountry_5G: 'CN', //	5.8G 国家代码
    wifiChannel_2G: 'auto', //	2.4G 信道
    wifiChannel_5G: 'auto', //	5.8G 信道
    wifiHwMode_2G: '11axg', //	2.4G协议，默认802.11ax
    wifiHwMode_5G: '11axa', //	5.8G协议，默认802.11ax
    wifiHtMode_2G: 'HT20', //	2.4G带宽
    wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
    wifiHtMode_5G: 'HT160', //	5.8G带宽
    wifiTxpower_2G: '', // 2.4G信号调节
    wifiTxpower_5G: '', // 5.8G信号调节
    wifi80211r_2G: '', //	2.4G快速漫游r协议
    wifi80211r_5G: '', //	5.8G快速漫游r协议
    wifiAuthmode_2G: 'mixed-psk', //	2.4G加密方式选项，默认wpa1/wpa2
    wifiAuthmode_5G: 'mixed-psk', //	5.8G加密方式选项，默认wpa1/wpa2
    wifiWpa3_2G: '', //	2.4G wpa3 开启标志
    wifiWpa3_5G: '', //	5.8G wpa3 开启标志
    wifiMaxsta_2G: '128',
    wifiMaxsta_5G: '128',

    // 双频合一的参数
    ssid: '', // SSID名称
    encryptionType: 'mixed-psk', // 加密类型，默认wpa1/wpa2
    password: '', // 密码
    isolate: false, // 隔离
    // 漫游配置
    quickRoaming: false, // 快速漫游
    roamingProtocol: undefined, // 漫游协议
    // 以下是暂不使用的变量
    networkLimit: undefined, // 限速
    upstreamLimit: '', // 上行限制
    downstreamLimit: '', // 下行限制
  })
}

onMounted(() => {
  getDeviceList()
  if (route.query.templateId)
    templateId = route.query.templateId as string

  // 启动定时器
  startRefreshTimer()
})

// 组件卸载时清理
onBeforeUnmount(() => {
  startTime.value = 0

  // 清理定时器
  stopRefreshTimer()
})

const channelOptions2g = computed(() => {
  const index = COUNTRY_OPTIONS_LOCALIZED.value.findIndex((data: any) => data.value === templateForm.wifiCountry_2G)

  return CHANNEL_ARR_2G[index] || []
})

const channelOptions5g = computed(() => {
  const index = COUNTRY_OPTIONS_LOCALIZED.value.findIndex((data: any) => data.value === templateForm.wifiCountry_5G)

  return CHANNEL_ARR_5G[index] || []
})

const roaming_protocol = [
  {
    label: '802.11r',
    value: 0,
  },
]

const selectedBandwidth = ref('20M')

watch(selectedBandwidth, newVal => {
  switch (newVal) {
    case '40M':
      templateForm.wifiHtMode_2G = 'HT40'
      templateForm.wifiForce40MHzMode_2G = '1'
      break
    case '20/40M':
      templateForm.wifiHtMode_2G = 'HT40'
      templateForm.wifiForce40MHzMode_2G = '0'
      break
    case '20M':
    default:
      templateForm.wifiHtMode_2G = 'HT20'
      templateForm.wifiForce40MHzMode_2G = '0'
      break
  }
}, { immediate: true })

const validatePassword = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.encryptionType === 'none')
    return true
  if (templateForm.ssid_type != 1)
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight = (value: string) => {
  if (templateForm.ssid_type === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight5G = (value: string) => {
  if (templateForm.ssid_type === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// 新增：根据5G信道动态计算可用的带宽选项
const BAND_WIDTH_5G_LOCALIZED = computed(() => {
  const channel = templateForm.wifiChannel_5G

  console.log('channel', channel)

  // 默认情况或自动信道：提供所有带宽选项
  if (!channel || channel === 'auto') {
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }

  const channelNum = Number.parseInt(channel, 10)

  // 根据信道范围返回相应的带宽选项
  if (channelNum >= 36 && channelNum <= 128) {
    // 36-128 可选20, 40, 80, 160
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }
  else if (channelNum === 132 || channelNum === 136) {
    // 132、136 可选20, 40
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
    ]
  }
  else if (channelNum === 140 || channelNum === 144) {
    // 140、144只能选20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
  else if (channelNum >= 149 && channelNum <= 161) {
    // 149-161 可选 20, 40, 80
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
    ]
  }
  else {
    // 161以上可选 20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
})

const changeChannel = () => {
  const newBandWidthOptions = BAND_WIDTH_5G_LOCALIZED.value
  const currentValue = templateForm.wifiHtMode_5G
  const isValidOption = newBandWidthOptions.some(option => option.value === currentValue)

  if (!isValidOption && newBandWidthOptions.length > 0) {
    // 如果当前选择不可用，则默认选择列表中第一个选项
    templateForm.wifiHtMode_5G = newBandWidthOptions[0].value
  }
}

const requiredValidatorNew = (value: unknown, message: string) => {
  if (templateForm.ssid_type === '1')
    return true

  if (isNullOrUndefined(value) || isEmptyArray(value) || value === false)
    return 'This field is required'

  return !!String(value).trim().length || message || 'This field is required'
}

const requiredValidatorNewTwo = (value: unknown, message: string) => {
  if (templateForm.ssid_type === '0')
    return true

  if (isNullOrUndefined(value) || isEmptyArray(value) || value === false)
    return 'This field is required'

  return !!String(value).trim().length || message || 'This field is required'
}

// 验证最大连接数：可以为空，或者是1-128之间的整数（包括1和128）
const validateMaxConnections = (value: string) => {
  // 如果为空，则通过验证
  if (!value || value.trim() === '')
    return true

  // 检查是否为数字
  const num = Number(value)
  if (isNaN(num) || !Number.isInteger(num))
    return t('Config.AP.MaxConnectionsIntegerError')

  // 检查范围是否在1-128之间
  if (num < 1 || num > 128)
    return t('Config.AP.MaxConnectionsRangeError')

  return true
}
</script>

<template>
  <div>
    <!-- 新建模板 -->
    <VNavigationDrawer
      v-if="createTemplateDialog"
      v-model="createTemplateDialog"
      persistent
      location="right"
      temporary
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ dialogStatus === 1 ? t('Config.AP.EditTemplate') : t('Config.AP.NewConfigTemplate') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="createTemplateDialog = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 - flex-grow-1确保占据剩余空间 -->
        <div class="flex-grow-1 d-flex flex-column overflow-hidden">
          <!-- 标签页固定 -->
          <div class="flex-shrink-0 px-6 pt-4">
            <VTabs
              v-model="currentTab"
              align-tabs="center"
              class="mb-4"
              fixed-tabs
            >
              <VTab
                v-for="(item, index) in tabList"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </VTab>
            </VTabs>
          </div>

          <!-- 内容区可滚动 -->
          <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar">
            <VWindow v-model="currentTab">
              <!-- 选择型号 -->
              <VWindowItem :value="0">
                <VForm ref="formRef3">
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.TemplateName') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.tpl_name"
                      :rules="[
                        v => !!v || t('Config.AP.TemplateNameLengthError'),
                        v => isByteLengthInRange(v, 1, 64) || t('Config.AP.TemplateNameLengthError'),
                      ]"
                      :placeholder="t('Config.AP.EnterTemplateName')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.Remark') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.description"
                      :rules="[v => isByteLengthInRange(v, 0, 128) || t('Config.AP.RemarkLengthError')]"
                      :placeholder="t('Config.AP.SelectRemark')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.SelectAPModel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.model"
                      :items="modelList"
                      :rules="[v => !!v || t('Config.AP.EnterAPModel')]"
                      class="mb-4"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.EnterAPModel')"
                    />
                  </div>
                </VForm>
              </VWindowItem>
              <!-- 基础配置 -->
              <VWindowItem :value="1">
                <VForm ref="formRef4">
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90">
                      {{ t('Config.AP.DualBandUnify') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.ssid_type"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                        @update:model-value="ssidTypeChange"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ t('Config.AP.DualBandUnifyHint') }}
                      </span>
                    </div>
                  </div>
                  <!-- 双频合一 -->
                  <div v-if="templateForm.ssid_type === '1'">
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid"
                        :rules="[(v: string) => requiredValidatorNewTwo(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.encryptionType"
                        :items="ENCRYPTION_TYPE"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                        item-title="label"
                        item-value="value"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.password"
                        :append-inner-icon="showMergePassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        :rules="[validatePassword]"
                        :type="showMergePassword ? 'text' : 'password'"
                        @click:append-inner="
                          showMergePassword = !showMergePassword
                        "
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Isolate') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.isolate"
                          class="mr-2"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{ templateForm.isolate ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <VDivider class="mb-4" />
                  <!-- 2.4G -->
                  <div class="text-primary text-h5 mb-4">
                    {{ t('Config.AP.WirelessSettings2G') }}
                  </div>
                  <div v-if="templateForm.ssid_type === '0'">
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.wifiOnOff_2G"
                          class="mr-2"
                          false-value="1"
                          true-value="0"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{
                            templateForm.wifiOnOff_2G === "0" ? t('Config.AP.On') : t('Config.AP.Off')
                          }}
                        </span>
                      </div>
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid_2g"
                        :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.AP.EnterSSID')"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.wifiAuthmode_2G"
                        :items="ENCRYPTION_TYPE"
                        :rules="templateForm.ssid_type === '0' ? [(v: string) => {
                          if (v === null) {
                            return t('Config.AP.SelectEncryption')
                          }
                        }] : []"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.AP.SelectEncryption')"
                      />
                    </div>
                    <div
                      v-if="templateForm.wifiAuthmode_2 != 'none'"
                      class="d-flex justify-space-between align-start mb-4"
                    >
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.key_2g"
                        :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'
                        "
                        :rules="[validatePasswordEight]"
                        :type="show2GPassword ? 'text' : 'password'"
                        :placeholder="t('Config.AP.EnterPassword')"
                        @click:append-inner="show2GPassword = !show2GPassword"
                      />
                    </div>
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Protocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHwMode_2G"
                      :items="PROTOCOL_2G"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectProtocol')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Country') }}
                    </div>
                    <VSelect
                      v-model="templateForm.wifiCountry_2G"
                      :items="COUNTRY_OPTIONS_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectCountry')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectCountry')"
                      @update:model-value="(val) => handleCountryChange('2G', val)"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Channel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiChannel_2G"
                      :items="channelOptions2g"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectChannel')
                        }
                      }]"
                      :placeholder="t('Config.AP.SelectChannel')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Bandwidth') }}
                    </div>
                    <AppSelect
                      v-model="selectedBandwidth"
                      :items="BAND_WIDTH_2G"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectBandwidth')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectBandwidth')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TxPower') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiTxpower_2G"
                      :items="TX_POWER_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.AP.SelectTxPower')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectTxPower')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.MaxConnections') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.wifiMaxsta_2G"
                      :rules="[validateMaxConnections]"
                      :placeholder="t('Config.AP.MaxConnectionsPlaceholder')"
                    />
                  </div>
                  <div
                    v-if="templateForm.ssid_type == '0'"
                    class="d-flex justify-space-between align-start mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.Isolate') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifiApIsolate_2G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{
                          templateForm.wifiApIsolate_2G == "0" ? t('Config.AP.Off') : t('Config.AP.On')
                        }}
                      </span>
                    </div>
                  </div>
                  <VDivider class="mb-4" />
                  <!-- 5G -->
                  <div class="text-primary text-h5 mb-4">
                    {{ t('Config.AP.WirelessSettings5G') }}
                  </div>
                  <div v-if="templateForm.ssid_type === '0'">
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.wifiOnOff_5G"
                          class="mr-2"
                          false-value="1"
                          true-value="0"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{
                            templateForm.wifiOnOff_5G === "0" ? t('Config.AP.On') : t('Config.AP.Off')
                          }}
                        </span>
                      </div>
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid_5g"
                        :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.AP.EnterSSID')"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.wifiAuthmode_5G"
                        :items="ENCRYPTION_TYPE"
                        :rules="templateForm.ssid_type === '0' ? [(v: string) => {
                          if (v === null) {
                            return t('Config.AP.SelectEncryption')
                          }
                        }] : []"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.AP.SelectEncryption')"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.key_5g"
                        :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'
                        "
                        :rules="[validatePasswordEight5G]"
                        :type="show5GPassword ? 'text' : 'password'"
                        :placeholder="t('Config.AP.EnterPassword')"
                        @click:append-inner="show5GPassword = !show5GPassword"
                      />
                    </div>
                  </div>

                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Protocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHwMode_5G"
                      :items="PROTOCOL_5G"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectProtocol')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Country') }}
                    </div>
                    <VSelect
                      v-model="templateForm.wifiCountry_5G"
                      :items="COUNTRY_OPTIONS_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectCountry')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectCountry')"
                      @update:model-value="(val) => handleCountryChange('5G', val)"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Channel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiChannel_5G"
                      :items="channelOptions5g"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectChannel')
                        }
                      }]"
                      :placeholder="t('Config.AP.SelectChannel')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Bandwidth') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHtMode_5G"
                      :items="BAND_WIDTH_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectBandwidth')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectBandwidth')"
                      @update:model-value="changeChannel"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TxPower') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiTxpower_5G"
                      :items="TX_POWER_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.AP.SelectTxPower')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectTxPower')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.MaxConnections') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.wifiMaxsta_5G"
                      :rules="[validateMaxConnections]"
                      :placeholder="t('Config.AP.MaxConnectionsPlaceholder')"
                    />
                  </div>
                  <div
                    v-if="templateForm.ssid_type === '0'"
                    class="d-flex justify-space-between align-start mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.Isolate') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifiApIsolate_5G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{
                          templateForm.wifiApIsolate_5G == "0" ? t('Config.AP.Off') : t('Config.AP.On')
                        }}
                      </span>
                    </div>
                  </div>
                </VForm>
              </VWindowItem>
              <VWindowItem :value="2">
                <VForm ref="formRef5">
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.NetworkType') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.net_type"
                      :items="NET_TYPE_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.AP.SelectNetworkType')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectNetworkType')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.SpeedLimit') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.networkLimit"
                      :items="SPEED_LIMIT_TYPE_LIST"
                      disabled
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectSpeedLimit')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.UpstreamLimit') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.upstreamLimit"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.AP.EnterUpstreamLimit')
                        }
                      }]"
                      disabled
                      :placeholder="t('Config.AP.EnterUpstreamLimit')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.DownstreamLimit') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.downstreamLimit"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.AP.EnterDownstreamLimit')
                        }
                      }]"
                      disabled
                      :placeholder="t('Config.AP.EnterDownstreamLimit')"
                    />
                  </div>
                </VForm>
              </VWindowItem>
              <VWindowItem :value="3">
                <VForm ref="formRef6">
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.QuickRoaming') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.quickRoaming"
                        class="mr-2"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ templateForm.quickRoaming ? t('Config.AP.On') : t('Config.AP.Off') }}
                      </span>
                    </div>
                  </div>
                  <div
                    v-if="templateForm.quickRoaming"
                    class="d-flex justify-space-start align-start"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.RoamingProtocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.roamingProtocol"
                      :items="roaming_protocol"
                      :rules="[(v: string) => {
                        if (templateForm.quickRoaming && v === null) {
                          return t('Config.AP.SelectRoamingProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.AP.SelectRoamingProtocol')"
                    />
                  </div>
                </VForm>
              </VWindowItem>
            </VWindow>
          </div>
        </div>

        <!-- 底部固定 -->
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-end">
          <VBtn
            color="secondary"
            variant="tonal"
            class="mr-2"
            @click="createTemplateDialog = false"
          >
            {{ t('Config.AP.Cancel') }}
          </VBtn>
          <VBtn
            v-if="currentTab !== tabList.length - 1"
            color="primary"
            variant="flat"
            @click="nextStepCreate"
          >
            {{ t('Config.AP.Next') }}
          </VBtn>
          <VBtn
            v-else
            color="primary"
            variant="flat"
            @click="save"
          >
            {{ t('Config.AP.SaveTemplate') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>
    <!-- 👉 list -->
    <VCard class="mb-6">
      <div class="d-flex flex-wrap gap-4 ma-6">
        <div class="d-flex align-center">
          <div class="cardTitle">
            {{ t('Config.AP.APConfigDistribution') }}
          </div>
        </div>
        <VSpacer />
        <div class="d-flex flex-wrap align-center gap-4">
          <VBtn
            color="success"
            prepend-icon="tabler-layout-grid-add"
            variant="tonal"
            @click="createNewTemplate"
          >
            {{ t('Config.AP.NewConfigTemplate') }}
          </VBtn>
          <VBtn
            color="primary"
            prepend-icon="tabler-file-export"
            @click="openDistributeTemplateDialog"
          >
            {{ t('Config.AP.SendTemplate') }}
          </VBtn>
        </div>
      </div>

      <VDivider class="mb-6" />
      <div class="d-flex flex-wrap gap-4 mx-6 justify-between">
        <AppSelect
          v-model="selectedModel"
          :items="modelList"
          item-title="label"
          item-value="value"
          :placeholder="t('Config.AP.PleaseSelectModel')"
          variant="outlined"
          density="compact"
          hide-details
          class="mr-4"
        />
        <AppTextField
          v-model="deviceName"
          :placeholder="t('Config.AP.SearchDevice')"
          variant="outlined"
          density="compact"
          hide-details
          class="mr-4"
        />
        <!--
          <div>
          <AppSelect
          v-model="itemsPerPage"
          :items="[5, 10, 20, 25, 50]"
          width="100"
          />
          </div>
        -->
      </div>
      <VDivider class="mt-6" />
      <VDataTableServer
        v-model="selectedRows"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="list"
        :loading="loading"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
        class="text-no-wrap"
        item-value="sn"
        show-select
        disable-sort
      >
        <template #item.type="{ item }">
          {{ item.type ? item.type : '--' }}
        </template>
        <template #item.onoff="{ item }">
          <VChip
            v-if="item.onoff === 'online'"
            label
            color="success"
            size="small"
          >
            {{ t('Config.AP.Online') }}
          </VChip>
          <VChip
            v-else
            label
            color="error"
            size="small"
          >
            {{ t('Config.AP.Offline') }}
          </VChip>
        </template>
        <!-- pagination -->
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalProduct"
          />
        </template>
      </VDataTableServer>
    </VCard>

    <VDialog
      v-if="distributeTemplateDialog"
      v-model="distributeTemplateDialog"
      :width="785"
    >
      <DialogCloseBtn @click="distributeTemplateDialog = false" />
      <VCard>
        <div class="px-8 py-8">
          <VCardText class="text-center text-h4">
            {{ t('Config.AP.DeployConfig') }}
          </VCardText>
          <VStepper
            v-model="step"
            class="elevation-0"
            non-linear
          >
            <VStepperHeader class="elevation-0">
              <VStepperItem
                :complete="step > 0"
                :value="0"
                class="font-weight-medium"
              >
                <template #icon="{ hasCompleted }">
                  <VIcon
                    v-if="hasCompleted"
                    icon="tabler-check"
                  />
                  <div
                    v-else
                    :class="[step === 0 && 'active']"
                    class="stepper-circle"
                  />
                </template>
                {{ t('Config.AP.ConfigSelection') }}
              </VStepperItem>

              <VDivider
                :class="[step > 0 && 'active']"
                class="stepper-line"
              />

              <VStepperItem
                :complete="step > 1"
                :value="1"
                class="font-weight-medium my-stepper-item"
              >
                <template #icon="{ hasCompleted }">
                  <VIcon
                    v-if="hasCompleted"
                    icon="tabler-check"
                  />
                  <div
                    v-else
                    :class="[step === 1 && 'active']"
                    class="stepper-circle"
                  />
                </template>
                {{ t('Config.AP.DistributionMethod') }}
              </VStepperItem>

              <VDivider
                :class="[step > 1 && 'active']"
                class="stepper-line"
              />

              <VStepperItem
                :complete="step >= 2"
                :value="2"
                class="font-weight-medium"
              >
                <template #icon="{ hasCompleted }">
                  <VIcon
                    v-if="hasCompleted"
                    icon="tabler-check"
                  />
                  <div
                    v-else
                    :class="[step === 2 && 'active']"
                    class="stepper-circle"
                  />
                </template>
                {{ t('Config.AP.Complete') }}
              </VStepperItem>
            </VStepperHeader>
            <VStepperWindow
              v-model="step"
              class="ma-0"
            >
              <VStepperWindowItem value="1">
                <VForm ref="formRef1">
                  <AppSelect
                    v-model="distributeForm.templateId"
                    :items="templateList"
                    :rules="[(v: string) => {
                      if (v === null) {
                        return t('Config.AP.SelectPreConfigTemplate')
                      }
                    }]"
                    class="mb-6"
                    item-title="tpl_name"
                    item-value="id"
                    :label="t('Config.AP.SelectPreConfigTemplate')"
                    :placeholder="t('Config.AP.SelectPreConfigTemplate')"
                  />
                </VForm>
                <span
                  class="go-to-create"
                  @click="createNewTemplateA"
                >{{ t('Config.AP.CreateNewTemplate') }}</span>
              </VStepperWindowItem>
              <VStepperWindowItem value="2">
                <VForm
                  v-if="distributeState === 0"
                  ref="formRef2"
                >
                  <AppSelect
                    v-model="distributeForm.distributeType"
                    :items="TEMPLATE_DISTRIBUTION_METHOD_LOCALIZED"
                    :rules="[(v: string) => {
                      if (v === null) {
                        return t('Config.AP.SelectDistributionMethod')
                      }
                    }]"
                    class="mb-6"
                    item-title="label"
                    item-value="value"
                    :label="t('Config.AP.SelectDistributionMethod')"
                    :placeholder="t('Config.AP.SelectDistributionMethod')"
                  />
                  <AppDateTimePicker
                    v-if="distributeForm.distributeType === 1"
                    v-model="distributeForm.distributeTime"
                    :config="{ enableTime: true, dateFormat: 'Y / m / d  H:i' }"
                    H:i
                    Y-m-d
                    :label="t('Config.AP.SelectDistributionTime')"
                    :placeholder="t('Config.AP.YearMonthDayTime')"
                  />
                </VForm>
                <div
                  v-else
                  class="d-flex flex-column align-center"
                >
                  <div class="mb-2 font-weight-medium">
                    {{ distributeState === 2 ? t('Config.AP.ConfigDistributionComplete') : t('Config.AP.ConfigDistributing') }}
                  </div>
                  <div class="progress d-flex align-center">
                    <VProgressLinear
                      :model-value="distributeProgress"
                      color="primary"
                    />
                    <div class="ml-4">
                      {{ distributeProgress }}%
                    </div>
                  </div>
                  <div class="distribute-log mt-4">
                    <div>{{ t('Config.AP.TotalDistribution') }} {{ totalNum }}</div>
                    <div>{{ t('Config.AP.Success') }} {{ successNum }} / {{ t('Config.AP.Failed') }} {{ failNum }}</div>
                  </div>
                </div>
              </VStepperWindowItem>
            </VStepperWindow>
          </VStepper>
        </div>
        <VCardText class="d-flex justify-center flex-wrap gap-3">
          <template v-if="distributeState === 0">
            <template v-if="step === 0">
              <VBtn
                color="secondary"
                variant="tonal"
                @click="closeDistributeTemplateDialog"
              >
                {{ t('Config.AP.Cancel') }}
              </VBtn>
              <VBtn
                color="primary"
                variant="flat"
                @click="nextStep"
              >
                {{ t('Config.AP.Next') }}
              </VBtn>
            </template>
            <template v-else>
              <VBtn
                color="secondary"
                variant="tonal"
                @click="step--"
              >
                {{ t('Config.AP.Previous') }}
              </VBtn>
              <VBtn
                :disabled="distributeDisabled"
                color="primary"
                variant="flat"
                @click="distributeHandle"
              >
                {{ t('Config.AP.ConfirmDeploy') }}
              </VBtn>
            </template>
          </template>
          <template v-else>
            <VBtn
              :disabled="distributeState !== 2"
              color="primary"
              variant="flat"
              @click="distributeSuccess"
            >
              {{ t('Config.AP.Complete') }}
            </VBtn>
            <VBtn
              :disabled="distributeState !== 2"
              color="info"
              variant="tonal"
              @click="exportLog"
            >
              {{ t('Config.AP.ExportLog') }}
            </VBtn>
          </template>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>

<style lang="scss" scoped>
.stepper-line {
  border-width: 3px;
  border-color: rgba(var(--v-theme-primary));
  border-radius: 3px;

  &.active {
    opacity: 1;
  }
}

.stepper-circle {
  border-radius: 50%;
  background-color: #fff;
  block-size: 14px;
  inline-size: 14px;

  &.active {
    block-size: 10px;
    inline-size: 10px;
  }
}

.go-to-create {
  color: rgb(var(--v-theme-primary));
  cursor: pointer;
  font-size: 13px;
}

.progress {
  inline-size: 328px;
}

.distribute-log {
  color: rgba(47, 43, 61, 55%);
  font-size: 13px;
  text-align: center;
  user-select: none;
}

.w-80px {
  inline-size: 80px;
}

.line-height-38px {
  line-height: 38px;
}

.flexBox {
  display: flex;
  align-items: center;
}

.associated-device {
  &:hover {
    text-decoration: underline;
  }
}

.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
</style>
